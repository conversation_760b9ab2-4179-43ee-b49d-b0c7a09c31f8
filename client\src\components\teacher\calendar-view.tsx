import { useState, useEffect } from 'react';
import { format, addDays, startOfWeek, isToday, isSameDay, parseISO, set } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from 'lucide-react';
import { TIME_SLOTS, SESSION_TYPES } from '@shared/schema';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/hooks/use-auth';
import { StatusBadge } from '@/components/ui/status-badge';

// Type pour les sessions
interface Session {
  id: number;
  date: string;
  timeSlot: string;
  type: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  status: string;
  teacherId: number;
  teacherName: string;
  className?: string;
  gradeLevel?: string;
  description?: string;
}

interface CalendarViewProps {
  onSlotClick: (date: Date, timeSlot: string) => void;
  onSessionClick: (session: Session) => void;
}

export function CalendarView({ onSlotClick, onSessionClick }: CalendarViewProps) {
  const { user } = useAuth();
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [weekDays, setWeekDays] = useState<Date[]>([]);

  // Récupérer les sessions de l'enseignant
  const { data: sessions = [] } = useQuery<Session[]>({
    queryKey: ['/api/sessions', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await apiRequest('GET', `/api/sessions/teacher/${user.id}`);
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des sessions');
      }
      return response.json();
    },
    enabled: !!user?.id
  });

  // Générer les jours de la semaine
  useEffect(() => {
    const days = generateWeekDays(currentWeek);
    setWeekDays(days);
  }, [currentWeek]);

  // Fonction pour générer les jours de la semaine
  const generateWeekDays = (date: Date) => {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = dimanche, 6 = samedi

    // Si on est le weekend (samedi ou dimanche), afficher la semaine précédente et la semaine suivante
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      // Obtenir le jeudi de la semaine précédente
      const thursdayLastWeek = startOfWeek(today, { weekStartsOn: 1 });
      thursdayLastWeek.setDate(thursdayLastWeek.getDate() + 3); // +3 jours depuis lundi = jeudi

      // Obtenir le lundi de la semaine suivante
      const mondayNextWeek = startOfWeek(today, { weekStartsOn: 1 });
      mondayNextWeek.setDate(mondayNextWeek.getDate() + 7); // +7 jours depuis lundi précédent = lundi suivant

      // Générer les jours à afficher (jeudi, vendredi, lundi, mardi, mercredi)
      const days = [];

      // Ajouter jeudi et vendredi de la semaine précédente
      days.push(new Date(thursdayLastWeek)); // Jeudi
      const fridayLastWeek = new Date(thursdayLastWeek);
      fridayLastWeek.setDate(fridayLastWeek.getDate() + 1);
      days.push(fridayLastWeek); // Vendredi

      // Ajouter lundi, mardi et mercredi de la semaine suivante
      days.push(new Date(mondayNextWeek)); // Lundi
      const tuesdayNextWeek = new Date(mondayNextWeek);
      tuesdayNextWeek.setDate(tuesdayNextWeek.getDate() + 1);
      days.push(tuesdayNextWeek); // Mardi
      const wednesdayNextWeek = new Date(mondayNextWeek);
      wednesdayNextWeek.setDate(wednesdayNextWeek.getDate() + 2);
      days.push(wednesdayNextWeek); // Mercredi

      return days;
    } else {
      // En semaine, afficher la semaine normale (lundi à vendredi)
      const monday = startOfWeek(date, { weekStartsOn: 1 });

      // Générer les 5 jours de la semaine (lundi à vendredi)
      const days = [];
      for (let i = 0; i < 5; i++) {
        days.push(addDays(monday, i));
      }

      return days;
    }
  };

  // Navigation dans le calendrier
  const goToPreviousWeek = () => {
    setCurrentWeek(prevWeek => {
      const monday = startOfWeek(prevWeek, { weekStartsOn: 1 });
      return addDays(monday, -7);
    });
  };

  const goToNextWeek = () => {
    setCurrentWeek(prevWeek => {
      const monday = startOfWeek(prevWeek, { weekStartsOn: 1 });
      return addDays(monday, 7);
    });
  };

  const goToToday = () => {
    setCurrentWeek(new Date());
  };

  // Vérifier si une session existe pour un jour et un créneau donnés
  const getSessionsForSlot = (date: Date, timeSlot: string) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return sessions.filter(session =>
      session.date === dateStr && session.timeSlot === timeSlot
    );
  };

  // Obtenir la couleur en fonction du type de session
  const getSessionColor = (type: string) => {
    const sessionType = SESSION_TYPES[type as keyof typeof SESSION_TYPES];
    return sessionType ? sessionType.color : 'gray';
  };

  // Vérifier si on est en mode weekend
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 = dimanche, 6 = samedi
  const isWeekendView = dayOfWeek === 0 || dayOfWeek === 6;

  return (
    <div className="space-y-4">
      {/* En-tête du calendrier avec navigation */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-2 mb-4">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={goToPreviousWeek}
            aria-label="Semaine précédente"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            onClick={goToToday}
            className="hidden sm:flex"
          >
            Aujourd'hui
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={goToNextWeek}
            aria-label="Semaine suivante"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex flex-col items-center">
          <h2 className="text-lg font-semibold flex items-center">
            <CalendarIcon className="mr-2 h-5 w-5" />
            <span>
              {isWeekendView
                ? "Vue weekend"
                : `Semaine du ${format(weekDays[0] || new Date(), 'dd MMMM', { locale: fr })}`}
            </span>
          </h2>

          {isWeekendView && (
            <p className="text-xs text-muted-foreground mt-1">
              Affichage des jours avant et après le weekend
            </p>
          )}
        </div>

        <Button
          variant="outline"
          onClick={goToToday}
          className="sm:hidden"
        >
          Aujourd'hui
        </Button>
      </div>

      {/* Grille du calendrier */}
      <div className="overflow-x-auto">
        <div className="min-w-[768px]">
          {/* En-têtes des jours */}
          <div className="grid grid-cols-6 gap-2">
            <div className="bg-muted rounded-md p-2 font-medium text-center">
              Horaires
            </div>
            {weekDays.map((day, index) => (
              <div
                key={index}
                className={`bg-muted rounded-md p-2 font-medium text-center ${
                  isToday(day) ? 'bg-blue-100 text-blue-800' : ''
                }`}
              >
                <div>{format(day, 'EEEE', { locale: fr })}</div>
                <div>{format(day, 'dd/MM', { locale: fr })}</div>
              </div>
            ))}
          </div>

          {/* Créneaux horaires */}
          {Object.entries(TIME_SLOTS).map(([slotId, slot]) => (
            <div key={slotId} className="grid grid-cols-6 gap-2 mt-2">
              {/* Libellé du créneau */}
              <div className="bg-muted rounded-md p-2 flex flex-col justify-center items-center">
                <div className="font-medium">{slot.label}</div>
                <div className="text-xs text-gray-500">{slot.time}</div>
              </div>

              {/* Cellules pour chaque jour */}
              {weekDays.map((day, dayIndex) => {
                const sessionsForSlot = getSessionsForSlot(day, slotId);

                return (
                  <div
                    key={`${slotId}-${dayIndex}`}
                    className={`border rounded-md p-1 min-h-[80px] cursor-pointer transition-colors hover:bg-gray-50 ${
                      isToday(day) ? 'border-blue-300' : 'border-gray-200'
                    }`}
                    onClick={() => onSlotClick(day, slotId)}
                  >
                    {sessionsForSlot.length > 0 ? (
                      <div className="h-full flex flex-col">
                        {sessionsForSlot.map(session => (
                          <Card
                            key={session.id}
                            className={`mb-1 border-l-4 border-l-${getSessionColor(session.type)} hover:shadow-md transition-shadow`}
                            onClick={(e) => {
                              e.stopPropagation();
                              onSessionClick(session);
                            }}
                          >
                            <CardContent className="p-2">
                              <div className="flex flex-col gap-1">
                                <div className="flex justify-between items-start">
                                  <Badge variant={getSessionColor(session.type) as any}>
                                    {SESSION_TYPES[session.type as keyof typeof SESSION_TYPES]?.label}
                                  </Badge>
                                  <StatusBadge status={session.status as any} className="text-[10px]" />
                                </div>
                                <div className="text-xs truncate">
                                  {session.type === 'RCD' && session.className && (
                                    <span>{session.className}</span>
                                  )}
                                  {session.type === 'DEVOIRS_FAITS' && session.gradeLevel && (
                                    <span>{session.gradeLevel}</span>
                                  )}
                                  {session.type === 'AUTRE' && session.description && (
                                    <span className="truncate">{session.description}</span>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="h-full flex items-center justify-center text-gray-400 text-xs">
                        Cliquez pour ajouter
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>

      {/* Légende */}
      <div className="flex flex-wrap gap-2 mt-4 justify-center">
        {Object.entries(SESSION_TYPES).map(([type, info]) => (
          <div key={type} className="flex items-center">
            <div className={`w-3 h-3 rounded-full bg-${info.color} mr-1`}></div>
            <span className="text-xs">{info.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
