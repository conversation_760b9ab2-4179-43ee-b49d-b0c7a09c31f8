import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  const [, setLocation] = useLocation();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 px-4">
      <div className="text-6xl font-bold text-gray-300 mb-4">404</div>
      <h1 className="text-2xl font-bold text-gray-800 mb-2">Page non trouvée</h1>
      <p className="text-gray-600 mb-8 text-center">
        La page que vous recherchez n'existe pas ou a été déplacée.
      </p>
      <Button onClick={() => setLocation('/')}>
        Retour à l'accueil
      </Button>
    </div>
  );
}
