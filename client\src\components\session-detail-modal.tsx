import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { 
  Dialog, DialogContent, DialogHeader, 
  DialogTitle, DialogFooter 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/ui/status-badge";
import { Badge } from "@/components/ui/badge";
import { SESSION_TYPES, TIME_SLOTS } from "@shared/schema";
import { useAuth } from '@/hooks/use-auth';
import { FileText, Download, Printer, Edit, Trash2 } from 'lucide-react';

// Type pour les sessions
interface Session {
  id: number;
  date: string;
  timeSlot: string;
  type: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  originalType: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  status: string;
  teacherId: number;
  teacherName: string;
  inPacte?: boolean;
  createdAt: string;
  updatedAt?: string;
  updatedBy?: string;
  
  // Fields specific to RCD
  replacedTeacherPrefix?: string;
  replacedTeacherLastName?: string;
  replacedTeacherFirstName?: string;
  replacedTeacherName?: string;
  className?: string;
  subject?: string;
  comment?: string;
  
  // Fields specific to Devoirs Faits
  studentCount?: number;
  gradeLevel?: string;
  
  // Fields specific to Autre
  description?: string;
}

interface SessionDetailModalProps {
  session: Session | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (session: Session) => void;
  onDelete?: (session: Session) => void;
  onExport?: (session: Session) => void;
  onPrint?: (session: Session) => void;
}

export function SessionDetailModal({ 
  session, 
  isOpen, 
  onClose,
  onEdit,
  onDelete,
  onExport,
  onPrint
}: SessionDetailModalProps) {
  const { user } = useAuth();
  
  if (!session) return null;

  // Vérifier si la session est éditable
  const isEditable = () => {
    if (!user) return false;
    
    // Les enseignants ne peuvent éditer que leurs propres sessions en statut SUBMITTED ou INCOMPLETE
    if (user.role === 'TEACHER') {
      return user.id === session.teacherId && 
             ['SUBMITTED', 'INCOMPLETE'].includes(session.status);
    }
    
    // Les administrateurs et la direction peuvent éditer toutes les sessions
    return ['ADMIN', 'PRINCIPAL'].includes(user.role);
  };

  // Vérifier si la session est supprimable
  const isDeletable = () => {
    if (!user) return false;
    
    // Les enseignants ne peuvent supprimer que leurs propres sessions en statut SUBMITTED ou INCOMPLETE
    if (user.role === 'TEACHER') {
      return user.id === session.teacherId && 
             ['SUBMITTED', 'INCOMPLETE'].includes(session.status);
    }
    
    // Les administrateurs et la direction peuvent supprimer les sessions non payées
    return ['ADMIN', 'PRINCIPAL'].includes(user.role) && 
           session.status !== 'PAID';
  };

  // Formater la date
  const formatDate = (dateStr: string) => {
    return format(parseISO(dateStr), 'dd MMMM yyyy', { locale: fr });
  };

  // Formater l'heure
  const formatTime = (timeSlot: string) => {
    return TIME_SLOTS[timeSlot as keyof typeof TIME_SLOTS]?.time || timeSlot;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span>Détails de la séance</span>
            <StatusBadge status={session.status as any} />
          </DialogTitle>
        </DialogHeader>

        <div className="py-4 space-y-6">
          {/* En-tête avec les informations principales */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 border-b pb-4">
            <div>
              <Badge variant={SESSION_TYPES[session.type as keyof typeof SESSION_TYPES]?.color as any} className="mb-2">
                {SESSION_TYPES[session.type as keyof typeof SESSION_TYPES]?.label}
              </Badge>
              
              {session.type !== session.originalType && (
                <div className="text-xs text-muted-foreground mt-1">
                  Type d'origine : {SESSION_TYPES[session.originalType as keyof typeof SESSION_TYPES]?.label}
                </div>
              )}
            </div>
            
            <div className="text-right">
              <div className="font-medium">{formatDate(session.date)}</div>
              <div className="text-sm text-muted-foreground">
                {formatTime(session.timeSlot)}
              </div>
            </div>
          </div>

          {/* Informations détaillées */}
          <div className="space-y-4">
            {/* Informations communes */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Enseignant</div>
                <div>{session.teacherName}</div>
              </div>
              
              {session.inPacte !== undefined && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Pacte</div>
                  <div>{session.inPacte ? 'Oui' : 'Non'}</div>
                </div>
              )}
            </div>

            {/* Informations spécifiques au type RCD */}
            {session.type === 'RCD' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Classe</div>
                    <div>{session.className}</div>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Enseignant remplacé</div>
                    <div>
                      {session.replacedTeacherPrefix} {session.replacedTeacherLastName} {session.replacedTeacherFirstName}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Informations spécifiques au type Devoirs Faits */}
            {session.type === 'DEVOIRS_FAITS' && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Niveau</div>
                  <div>{session.gradeLevel}</div>
                </div>
                
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Nombre d'élèves</div>
                  <div>{session.studentCount}</div>
                </div>
              </div>
            )}

            {/* Informations spécifiques au type Autre */}
            {session.type === 'AUTRE' && (
              <div>
                <div className="text-sm font-medium text-muted-foreground">Description</div>
                <div>{session.description}</div>
              </div>
            )}

            {/* Commentaire (si présent) */}
            {session.comment && (
              <div>
                <div className="text-sm font-medium text-muted-foreground">Commentaire</div>
                <div className="whitespace-pre-line text-sm">{session.comment}</div>
              </div>
            )}

            {/* Informations de suivi */}
            <div className="border-t pt-4 mt-4 text-xs text-muted-foreground">
              <div>Créée le {format(parseISO(session.createdAt), 'dd/MM/yyyy à HH:mm', { locale: fr })}</div>
              
              {session.updatedAt && session.updatedBy && (
                <div>
                  Dernière modification le {format(parseISO(session.updatedAt), 'dd/MM/yyyy à HH:mm', { locale: fr })} par {session.updatedBy}
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-wrap gap-2">
          <div className="flex-1 flex flex-wrap gap-2">
            {onExport && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onExport(session)}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                <span className="hidden sm:inline">Exporter</span>
              </Button>
            )}
            
            {onPrint && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onPrint(session)}
                className="flex items-center gap-1"
              >
                <Printer className="h-4 w-4" />
                <span className="hidden sm:inline">Imprimer</span>
              </Button>
            )}
          </div>
          
          <div className="flex gap-2">
            {isEditable() && onEdit && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onEdit(session)}
                className="flex items-center gap-1"
              >
                <Edit className="h-4 w-4" />
                <span>Modifier</span>
              </Button>
            )}
            
            {isDeletable() && onDelete && (
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => onDelete(session)}
                className="flex items-center gap-1"
              >
                <Trash2 className="h-4 w-4" />
                <span>Supprimer</span>
              </Button>
            )}
            
            <Button 
              variant="default" 
              size="sm"
              onClick={onClose}
            >
              Fermer
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
