# 🚀 Scripts de lancement SupChaissac

Voici les scripts créés pour faciliter le lancement de votre application :

## 📁 Scripts disponibles

### 🟢 `start-complete.bat` ⭐ **RECOMMANDÉ**
**Double-cliquez sur ce fichier pour tout faire automatiquement !**
- ✅ Installe les dépendances si nécessaire
- ✅ Démarre le serveur
- ✅ Ouvre automatiquement le navigateur
- ✅ Tout-en-un !

### 🔧 `install-dependencies.bat`
- Installe uniquement les dépendances npm
- À utiliser si vous avez des problèmes de dépendances

### 🖥️ `start-server.bat`
- Démarre uniquement le serveur de développement
- À utiliser si vous voulez juste lancer le serveur

### 🌐 `open-browser.bat`
- Ouvre uniquement le navigateur sur http://localhost:5000
- À utiliser si le serveur est déjà lancé

## 🎯 Utilisation recommandée

1. **Premier lancement** : Double-cliquez sur `start-complete.bat`
2. **Lancements suivants** : Double-cliquez sur `start-complete.bat` ou `start-server.bat`

## 🔗 URL de l'application
Une fois le serveur lancé, votre application sera disponible sur :
**http://localhost:5000**

## ⚠️ Prérequis
- Node.js doit être installé sur votre système
- Si Node.js n'est pas installé, téléchargez-le depuis : https://nodejs.org/

## 🛑 Arrêter le serveur
Pour arrêter le serveur, appuyez sur `Ctrl + C` dans la fenêtre du terminal qui s'est ouverte.

---
*Scripts créés pour faciliter le développement de SupChaissac* 🎉
