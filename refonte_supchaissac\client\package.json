{"name": "supchaissac-client", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "typescript": "^5.0.2", "vite": "^4.4.5", "tailwindcss": "^3.3.3", "postcss": "^8.4.31", "autoprefixer": "^10.4.16"}}