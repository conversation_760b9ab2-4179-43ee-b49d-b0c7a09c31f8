import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TeacherManagement } from './TeacherManagement';
import { LayoutDashboard, Users, Calendar, FileText } from 'lucide-react';

export function SecretaryView() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("dashboard");

  return (
    <div className="min-h-screen bg-gray-100 pt-16 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col space-y-4">
          <header className="bg-white shadow-sm rounded-lg p-4">
            <h1 className="text-2xl font-bold text-gray-900">
              B<PERSON><PERSON><PERSON>, {user?.name}
            </h1>
            <p className="text-gray-600">
              Interface de gestion du secrétariat
            </p>
          </header>

          <div className="bg-white shadow-sm rounded-lg">
            <Tabs defaultValue="dashboard" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="border-b px-4">
                <TabsList className="bg-transparent h-auto p-0 w-full justify-start">
                  <TabsTrigger
                    value="dashboard"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <LayoutDashboard className="h-4 w-4" />
                    <span>Tableau de bord</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="sessions"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <Calendar className="h-4 w-4" />
                    <span>Toutes les séances</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="teachers"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <Users className="h-4 w-4" />
                    <span>Enseignants</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="reports"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <FileText className="h-4 w-4" />
                    <span>Rapports</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <div className="p-4">
                <TabsContent value="dashboard" className="mt-0 pt-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Tableau de bord</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p>Le tableau de bord sera implémenté ici.</p>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="sessions" className="mt-0 pt-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Toutes les séances</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p>La liste des séances sera implémentée ici.</p>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="teachers" className="mt-0 pt-2">
                  <TeacherManagement />
                </TabsContent>

                <TabsContent value="reports" className="mt-0 pt-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Rapports</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p>Les rapports seront implémentés ici.</p>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
