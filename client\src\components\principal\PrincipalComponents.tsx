import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";

export function PrincipalView() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("dashboard");

  return (
    <div className="min-h-screen bg-gray-100 pt-16 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col space-y-4">
          <header className="bg-white shadow-sm rounded-lg p-4">
            <h1 className="text-2xl font-bold text-gray-900">
              Bienvenue, {user?.name}
            </h1>
            <p className="text-gray-600">
              Interface de direction
            </p>
          </header>

          <div className="bg-white shadow-sm rounded-lg p-4">
            <div className="flex space-x-2 border-b pb-2 mb-4">
              <Button
                variant={activeTab === "dashboard" ? "default" : "ghost"}
                onClick={() => setActiveTab("dashboard")}
              >
                Tableau de bord
              </Button>
              <Button
                variant={activeTab === "validation" ? "default" : "ghost"}
                onClick={() => setActiveTab("validation")}
              >
                Validation
              </Button>
              <Button
                variant={activeTab === "reports" ? "default" : "ghost"}
                onClick={() => setActiveTab("reports")}
              >
                Rapports
              </Button>
              <Button
                variant={activeTab === "settings" ? "default" : "ghost"}
                onClick={() => setActiveTab("settings")}
              >
                Paramètres
              </Button>
            </div>

            <div className="py-4">
              {activeTab === "dashboard" && (
                <Card>
                  <CardHeader>
                    <CardTitle>Tableau de bord</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>Le tableau de bord sera implémenté ici.</p>
                  </CardContent>
                </Card>
              )}

              {activeTab === "validation" && (
                <Card>
                  <CardHeader>
                    <CardTitle>Validation des séances</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>L'interface de validation sera implémentée ici.</p>
                  </CardContent>
                </Card>
              )}

              {activeTab === "reports" && (
                <Card>
                  <CardHeader>
                    <CardTitle>Rapports</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>Les rapports seront implémentés ici.</p>
                  </CardContent>
                </Card>
              )}

              {activeTab === "settings" && (
                <Card>
                  <CardHeader>
                    <CardTitle>Paramètres</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>Les paramètres seront implémentés ici.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
