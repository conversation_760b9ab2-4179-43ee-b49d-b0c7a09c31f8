{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.3"}, "devDependencies": {"@types/node": "^20.8.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "typescript": "^5.0.2", "vite": "^4.4.5"}}