import { QueryClient } from '@tanstack/react-query';

// Création du client de requêtes
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60, // 1 minute
      retry: 1,
    },
  },
});

// Fonction utilitaire pour les requêtes API
export async function apiRequest(
  method: string,
  url: string,
  data?: any,
  options?: RequestInit
) {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  const config: RequestInit = {
    method,
    headers,
    credentials: 'include',
    ...options,
  };

  if (data) {
    config.body = JSON.stringify(data);
  }

  return fetch(url, config);
}
