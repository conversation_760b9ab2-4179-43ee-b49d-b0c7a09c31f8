import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';

// Type pour l'utilisateur
interface User {
  id: number;
  username: string;
  name: string;
  role: 'TEACHER' | 'SECRETARY' | 'PRINCIPAL' | 'ADMIN';
  initials?: string;
  signature?: string | null;
  inPacte?: boolean;
}

// Type pour les informations de connexion
interface LoginCredentials {
  username: string;
  password: string;
}

// Type pour le contexte d'authentification
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  loginMutation: ReturnType<typeof useMutation<User, Error, LoginCredentials>>;
  logoutMutation: ReturnType<typeof useMutation<void, Error, void>>;
}

// Création du contexte d'authentification
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider pour le contexte d'authentification
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Requête pour récupérer l'utilisateur actuel
  const { data: currentUser, isLoading, error: fetchError } = useQuery({
    queryKey: ['currentUser'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/user');
        if (!response.ok) {
          if (response.status === 401) {
            return null; // Non authentifié, pas d'erreur
          }
          throw new Error('Erreur lors de la récupération de l\'utilisateur');
        }
        return await response.json();
      } catch (error) {
        console.error('Erreur lors de la récupération de l\'utilisateur:', error);
        return null;
      }
    },
    retry: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Mutation pour la connexion
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      const response = await apiRequest('POST', '/api/login', credentials);
      if (!response.ok) {
        throw new Error('Identifiants incorrects');
      }
      return await response.json();
    },
    onSuccess: (data) => {
      setUser(data);
      queryClient.setQueryData(['currentUser'], data);
      setError(null);
    },
    onError: (error: Error) => {
      setError(error);
    },
  });

  // Mutation pour la déconnexion
  const logoutMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/logout');
      if (!response.ok) {
        throw new Error('Erreur lors de la déconnexion');
      }
    },
    onSuccess: () => {
      setUser(null);
      queryClient.setQueryData(['currentUser'], null);
      queryClient.invalidateQueries();
    },
    onError: (error: Error) => {
      setError(error);
    },
  });

  // Mettre à jour l'utilisateur lorsque les données sont récupérées
  useEffect(() => {
    if (currentUser) {
      setUser(currentUser);
    }
  }, [currentUser]);

  // Mettre à jour l'erreur lorsqu'une erreur est détectée
  useEffect(() => {
    if (fetchError) {
      setError(fetchError as Error);
    }
  }, [fetchError]);

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        error,
        loginMutation,
        logoutMutation,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Hook pour utiliser le contexte d'authentification
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
