// Adapted from shadcn/ui toast component
import { Toast, ToastActionElement, ToastProps } from "@/components/ui/toast"

import {
  useToast as useToastOriginal,
  type ToastOptions as ToastOptionsOriginal,
} from "@/components/ui/use-toast"

type ToastOptions = ToastOptionsOriginal & {
  variant?: ToastProps["variant"]
}

export function useToast() {
  const { toast, dismiss, toasts } = useToastOriginal()

  function customToast({
    variant,
    ...props
  }: ToastOptions & {
    title?: React.ReactNode
    description?: React.ReactNode
    action?: ToastActionElement
  }) {
    return toast({
      ...props,
      className: variant ? `${variant}` : "",
    })
  }

  return {
    toast: customToast,
    dismiss,
    toasts,
  }
}
