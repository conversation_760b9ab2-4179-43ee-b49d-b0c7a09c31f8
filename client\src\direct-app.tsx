import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

// Types pour les données simulées
interface Session {
  id: number;
  date: string;
  timeSlot: string;
  type: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  status: 'PENDING' | 'VALIDATED' | 'REJECTED' | 'PAID';
  description?: string;
  className?: string;
  subject?: string;
  studentCount?: number;
  comment?: string;
}

// Définition des profils utilisateurs pour les tests
const TEST_PROFILES = [
  { 
    id: 1,
    name: '<PERSON>', 
    role: 'Enseignante sans pacte', 
    username: '<EMAIL>',
    userRole: 'TEACHER',
    inPacte: false
  },
  { 
    id: 2,
    name: '<PERSON>', 
    role: 'Enseignante avec pacte', 
    username: '<EMAIL>',
    userRole: 'TEACHER',
    inPacte: true
  },
  { 
    id: 3,
    name: '<PERSON>', 
    role: 'Enseignant sans pacte', 
    username: '<EMAIL>',
    userRole: 'TEACHER',
    inPacte: false
  },
  { 
    id: 4,
    name: '<PERSON>', 
    role: 'Enseignant avec pacte', 
    username: '<EMAIL>',
    userRole: 'TEACHER',
    inPacte: true
  },
  { 
    id: 5,
    name: 'Laure Martin', 
    role: 'Secrétariat', 
    username: '<EMAIL>',
    userRole: 'SECRETARY',
    inPacte: false
  },
  { 
    id: 6,
    name: 'Jean Dupont', 
    role: 'Direction', 
    username: '<EMAIL>',
    userRole: 'PRINCIPAL',
    inPacte: false
  },
  { 
    id: 7,
    name: 'Admin', 
    role: 'Administrateur', 
    username: '<EMAIL>',
    userRole: 'ADMIN',
    inPacte: false
  },
];

// Données simulées pour les sessions
const MOCK_SESSIONS: Session[] = [
  {
    id: 1,
    date: '2023-11-15',
    timeSlot: '08:00-09:00',
    type: 'RCD',
    status: 'VALIDATED',
    className: '3ème B',
    subject: 'Mathématiques'
  },
  {
    id: 2,
    date: '2023-11-16',
    timeSlot: '10:00-11:00',
    type: 'DEVOIRS_FAITS',
    status: 'PENDING',
    studentCount: 12
  },
  {
    id: 3,
    date: '2023-11-17',
    timeSlot: '14:00-15:00',
    type: 'AUTRE',
    status: 'REJECTED',
    description: 'Réunion pédagogique',
    comment: 'Hors cadre des heures supplémentaires'
  },
  {
    id: 4,
    date: '2023-11-20',
    timeSlot: '16:00-17:00',
    type: 'HSE',
    status: 'PAID',
    description: 'Soutien scolaire'
  }
];

function DirectApp() {
  const [selectedProfile, setSelectedProfile] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  
  // États pour la vue enseignant
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sessions] = useState<Session[]>(MOCK_SESSIONS);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleProfileSelect = (index: number) => {
    setSelectedProfile(index);
  };

  const handleLogin = () => {
    if (selectedProfile === null) return;
    
    setIsLoading(true);
    
    // Simuler une connexion
    setTimeout(() => {
      const profile = TEST_PROFILES[selectedProfile];
      
      // Stocker les informations de l'utilisateur
      setCurrentUser({
        id: profile.id,
        username: profile.username,
        name: profile.name,
        role: profile.userRole,
        inPacte: profile.inPacte
      });
      
      setIsLoggedIn(true);
      setIsLoading(false);
    }, 800);
  };

  const handleLogout = () => {
    setCurrentUser(null);
    setIsLoggedIn(false);
    setSelectedProfile(null);
  };

  // Fonctions pour la vue enseignant
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'VALIDATED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      case 'PAID': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING': return 'En attente';
      case 'VALIDATED': return 'Validée';
      case 'REJECTED': return 'Refusée';
      case 'PAID': return 'Payée';
      default: return status;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'RCD': return 'Remplacement courte durée';
      case 'DEVOIRS_FAITS': return 'Devoirs faits';
      case 'HSE': return 'HSE';
      case 'AUTRE': return 'Autre';
      default: return type;
    }
  };

  const handleSessionClick = (session: Session) => {
    setSelectedSession(session);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedSession(null);
  };

  // Calcul des statistiques
  const pendingCount = sessions.filter(s => s.status === 'PENDING').length;
  const validatedCount = sessions.filter(s => s.status === 'VALIDATED').length;
  const rejectedCount = sessions.filter(s => s.status === 'REJECTED').length;
  const paidCount = sessions.filter(s => s.status === 'PAID').length;

  // Page de connexion
  if (!isLoggedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-100 to-white">
        <div className="w-full max-w-md bg-white rounded-lg shadow-xl overflow-hidden">
          {/* En-tête */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-500 px-6 py-8 text-white">
            <h1 className="text-3xl font-bold text-center mb-2">Gestion des Heures Supplémentaires</h1>
            <p className="text-center text-blue-100">
              Veuillez sélectionner un profil pour vous connecter
            </p>
          </div>
          
          {/* Contenu */}
          <div className="p-6">
            <h2 className="text-xl font-semibold text-blue-800 mb-4 text-center">
              Choisissez un profil pour tester
            </h2>
            
            <div className="space-y-2 mb-6">
              {TEST_PROFILES.map((profile, index) => (
                <button
                  key={index}
                  className={`w-full text-left px-4 py-3 rounded-md transition-all ${
                    selectedProfile === index
                      ? 'bg-blue-600 text-white'
                      : 'bg-white border border-blue-200 hover:border-blue-400 hover:bg-blue-50'
                  }`}
                  onClick={() => handleProfileSelect(index)}
                >
                  <div className="font-medium">{profile.name}</div>
                  <div className={`text-sm ${selectedProfile === index ? 'text-blue-100' : 'text-blue-600'}`}>
                    {profile.role}
                  </div>
                </button>
              ))}
            </div>
            
            <button
              className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                isLoading
                  ? 'bg-blue-400 cursor-not-allowed'
                  : selectedProfile === null
                  ? 'bg-blue-300 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
              onClick={handleLogin}
              disabled={isLoading || selectedProfile === null}
            >
              {isLoading ? 'Connexion en cours...' : 'Se connecter maintenant'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Page d'accueil après connexion
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Barre de navigation */}
      <header className="bg-white shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <h1 className="text-xl font-semibold text-gray-900">
            Gestion des Heures Supplémentaires
          </h1>
          <div className="flex items-center space-x-4">
            <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
              {currentUser.role === "TEACHER" && `Enseignant${currentUser.inPacte ? " (avec pacte)" : " (sans pacte)"}`}
              {currentUser.role === "SECRETARY" && "Secrétariat"}
              {currentUser.role === "PRINCIPAL" && "Direction"}
              {currentUser.role === "ADMIN" && "Administration"}
            </div>
            <button
              className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-100"
              onClick={handleLogout}
            >
              Déconnexion
            </button>
          </div>
        </div>
      </header>

      {/* Contenu principal basé sur le rôle */}
      {currentUser.role === "TEACHER" ? (
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Carte de bienvenue */}
          <div className="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Bienvenue, {currentUser.name}
            </h2>
            <p className="text-gray-600 mb-4">
              Gérez vos heures supplémentaires et suivez leur statut.
            </p>
            <div className="flex flex-wrap gap-4 mt-4">
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Déclarer des heures
              </button>
            </div>
          </div>

          {/* Onglets */}
          <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
            <div className="border-b border-gray-200">
              <nav className="flex -mb-px">
                <button
                  className={`py-4 px-6 font-medium text-sm ${
                    activeTab === 'dashboard'
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab('dashboard')}
                >
                  Tableau de bord
                </button>
                <button
                  className={`py-4 px-6 font-medium text-sm ${
                    activeTab === 'sessions'
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab('sessions')}
                >
                  Mes séances
                </button>
                <button
                  className={`py-4 px-6 font-medium text-sm ${
                    activeTab === 'calendar'
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab('calendar')}
                >
                  Calendrier
                </button>
                <button
                  className={`py-4 px-6 font-medium text-sm ${
                    activeTab === 'profile'
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab('profile')}
                >
                  Mon profil
                </button>
              </nav>
            </div>

            <div className="p-6">
              {/* Contenu du tableau de bord */}
              {activeTab === 'dashboard' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Résumé de vos heures</h3>
                  
                  {/* Statistiques */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="text-sm text-gray-500">En attente</div>
                      <div className="text-2xl font-bold text-yellow-600">{pendingCount}</div>
                    </div>
                    <div className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="text-sm text-gray-500">Validées</div>
                      <div className="text-2xl font-bold text-green-600">{validatedCount}</div>
                    </div>
                    <div className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="text-sm text-gray-500">Refusées</div>
                      <div className="text-2xl font-bold text-red-600">{rejectedCount}</div>
                    </div>
                    <div className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="text-sm text-gray-500">Payées</div>
                      <div className="text-2xl font-bold text-blue-600">{paidCount}</div>
                    </div>
                  </div>

                  {/* Séances récentes */}
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Séances récentes</h3>
                  <div className="border rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {sessions.slice(0, 3).map((session) => (
                          <tr key={session.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {format(new Date(session.date), 'dd MMMM yyyy', { locale: fr })} ({session.timeSlot})
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {getTypeLabel(session.type)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(session.status)}`}>
                                {getStatusLabel(session.status)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <button 
                                className="text-blue-600 hover:text-blue-800"
                                onClick={() => handleSessionClick(session)}
                              >
                                Voir détails
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  
                  {sessions.length > 3 && (
                    <div className="mt-4 text-right">
                      <button 
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        onClick={() => setActiveTab('sessions')}
                      >
                        Voir toutes les séances →
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Liste complète des séances */}
              {activeTab === 'sessions' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Toutes mes séances</h3>
                  <div className="border rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Détails</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {sessions.map((session) => (
                          <tr key={session.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {format(new Date(session.date), 'dd MMMM yyyy', { locale: fr })} <br />
                              <span className="text-gray-500">{session.timeSlot}</span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {getTypeLabel(session.type)}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {session.type === 'RCD' && session.className && (
                                <div>Classe: {session.className} <br /> Matière: {session.subject}</div>
                              )}
                              {session.type === 'DEVOIRS_FAITS' && (
                                <div>Nombre d'élèves: {session.studentCount}</div>
                              )}
                              {session.type === 'AUTRE' && session.description && (
                                <div>{session.description}</div>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(session.status)}`}>
                                {getStatusLabel(session.status)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <button 
                                className="text-blue-600 hover:text-blue-800"
                                onClick={() => handleSessionClick(session)}
                              >
                                Voir détails
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Vue calendrier */}
              {activeTab === 'calendar' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Calendrier des séances</h3>
                  <div className="bg-gray-100 p-8 rounded-lg text-center">
                    <p className="text-gray-500">Vue calendrier (à implémenter)</p>
                    <p className="mt-2 text-sm text-gray-400">Cette fonctionnalité sera disponible prochainement</p>
                  </div>
                </div>
              )}

              {/* Profil */}
              {activeTab === 'profile' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Mon profil</h3>
                  <div className="bg-white border rounded-lg p-6 space-y-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Nom complet</h4>
                      <p className="text-gray-900">{currentUser.name}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Email</h4>
                      <p className="text-gray-900">{currentUser.username}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Statut Pacte</h4>
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full ${currentUser.inPacte ? 'bg-green-500' : 'bg-gray-400'} mr-2`}></div>
                        <p className="text-gray-900">
                          {currentUser.inPacte ? 'Je participe au pacte' : 'Je ne participe pas au pacte'}
                        </p>
                      </div>
                    </div>
                    {currentUser.inPacte && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-500 mb-1">Progression Pacte</h4>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Heures effectuées</span>
                          <span className="font-medium">12 / 36 heures</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-green-600 h-2.5 rounded-full" style={{ width: '33%' }}></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      ) : (
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Bienvenue, {currentUser.name}
            </h2>
            <p className="text-gray-600 mb-4">
              Vous êtes connecté en tant que {
                currentUser.role === "SECRETARY" ? "secrétariat" : 
                currentUser.role === "PRINCIPAL" ? "direction" : "administrateur"
              }.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 text-blue-800">
              <p className="font-medium">Interface de démonstration</p>
              <p className="text-sm mt-1">
                Cette interface simplifiée est destinée à la phase de développement et de test.
                Les fonctionnalités pour ce rôle seront implémentées prochainement.
              </p>
            </div>
          </div>
        </main>
      )}

      {/* Modale de détails de session */}
      {isModalOpen && selectedSession && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Détails de la séance
              </h3>
              <button 
                className="text-gray-400 hover:text-gray-500"
                onClick={handleCloseModal}
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Date et heure</h4>
                <p className="text-gray-900">
                  {format(new Date(selectedSession.date), 'dd MMMM yyyy', { locale: fr })} ({selectedSession.timeSlot})
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Type</h4>
                <p className="text-gray-900">{getTypeLabel(selectedSession.type)}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Statut</h4>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedSession.status)}`}>
                  {getStatusLabel(selectedSession.status)}
                </span>
              </div>
              
              {/* Détails spécifiques au type */}
              {selectedSession.type === 'RCD' && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Détails du remplacement</h4>
                  <p className="text-gray-900">
                    Classe: {selectedSession.className} <br />
                    Matière: {selectedSession.subject}
                  </p>
                </div>
              )}
              
              {selectedSession.type === 'DEVOIRS_FAITS' && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Détails</h4>
                  <p className="text-gray-900">
                    Nombre d'élèves: {selectedSession.studentCount}
                  </p>
                </div>
              )}
              
              {selectedSession.type === 'AUTRE' && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Description</h4>
                  <p className="text-gray-900">{selectedSession.description}</p>
                </div>
              )}
              
              {/* Commentaire */}
              {selectedSession.comment && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Commentaire</h4>
                  <p className="text-gray-900">{selectedSession.comment}</p>
                </div>
              )}
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button 
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                onClick={handleCloseModal}
              >
                Fermer
              </button>
              {selectedSession.status === 'PENDING' && (
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Modifier
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DirectApp;
