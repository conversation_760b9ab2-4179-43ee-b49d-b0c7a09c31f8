import { cn } from "@/lib/utils";
import { SESSION_STATUS } from "@shared/schema";

interface StatusBadgeProps {
  status: keyof typeof SESSION_STATUS;
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const statusInfo = SESSION_STATUS[status];
  
  const getStatusColor = () => {
    switch (statusInfo.color) {
      case 'gray':
        return 'bg-gray-100 text-gray-800';
      case 'amber':
        return 'bg-amber-100 text-amber-800';
      case 'blue':
        return 'bg-blue-100 text-blue-800';
      case 'green':
        return 'bg-green-100 text-green-800';
      case 'validated':
        return 'bg-validated text-validated-foreground';
      case 'paid':
        return 'bg-paid text-paid-foreground';
      case 'destructive':
        return 'bg-destructive/10 text-destructive';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <span
      className={cn(
        "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
        getStatusColor(),
        className
      )}
      title={statusInfo.description}
    >
      {statusInfo.label}
    </span>
  );
}
