{"name": "teacher-hours", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-slider": "^1.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.4", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "lucide-react": "^0.285.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-signature-canvas": "^1.0.6", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}