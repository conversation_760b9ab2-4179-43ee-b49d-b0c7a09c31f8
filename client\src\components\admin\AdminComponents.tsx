import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";

export function AdminView() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("users");

  return (
    <div className="min-h-screen bg-gray-100 pt-16 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col space-y-4">
          <header className="bg-white shadow-sm rounded-lg p-4">
            <h1 className="text-2xl font-bold text-gray-900">
              Administration
            </h1>
            <p className="text-gray-600">
              Gestion des utilisateurs et des paramètres système
            </p>
          </header>

          <div className="bg-white shadow-sm rounded-lg p-4">
            <div className="flex space-x-2 border-b pb-2 mb-4">
              <Button
                variant={activeTab === "users" ? "default" : "ghost"}
                onClick={() => setActiveTab("users")}
              >
                Utilisateurs
              </Button>
              <Button
                variant={activeTab === "settings" ? "default" : "ghost"}
                onClick={() => setActiveTab("settings")}
              >
                Paramètres système
              </Button>
              <Button
                variant={activeTab === "logs" ? "default" : "ghost"}
                onClick={() => setActiveTab("logs")}
              >
                Journaux
              </Button>
            </div>

            <div className="py-4">
              {activeTab === "users" && (
                <Card>
                  <CardHeader>
                    <CardTitle>Gestion des utilisateurs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>La gestion des utilisateurs sera implémentée ici.</p>
                  </CardContent>
                </Card>
              )}

              {activeTab === "settings" && (
                <Card>
                  <CardHeader>
                    <CardTitle>Paramètres système</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>Les paramètres système seront implémentés ici.</p>
                  </CardContent>
                </Card>
              )}

              {activeTab === "logs" && (
                <Card>
                  <CardHeader>
                    <CardTitle>Journaux d'activité</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>Les journaux d'activité seront implémentés ici.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
