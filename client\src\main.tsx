import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';

// Version ultra-simple qui fonctionne à coup sûr
function WorkingApp() {
  const [user, setUser] = React.useState(null);

  if (!user) {
    return (
      <div style={{ padding: '40px', maxWidth: '400px', margin: '100px auto', fontFamily: 'Arial' }}>
        <h1>Gestion des Heures Supplémentaires</h1>
        <h2>Choisissez un profil</h2>
        <div style={{ marginTop: '20px' }}>
          <button
            onClick={() => setUser('<PERSON>')}
            style={{ display: 'block', width: '100%', padding: '10px', margin: '10px 0', backgroundColor: '#3b82f6', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}
          >
            <PERSON> (<PERSON>)
          </button>
          <button
            onClick={() => setUser('<PERSON>')}
            style={{ display: 'block', width: '100%', padding: '10px', margin: '10px 0', backgroundColor: '#10b981', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}
          >
            Jean Dupont (Direction)
          </button>
          <button
            onClick={() => setUser('Admin')}
            style={{ display: 'block', width: '100%', padding: '10px', margin: '10px 0', backgroundColor: '#8b5cf6', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}
          >
            Admin (Administrateur)
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #ccc', paddingBottom: '10px', marginBottom: '20px' }}>
        <h1>Gestion des Heures Supplémentaires</h1>
        <div>
          <span style={{ marginRight: '10px' }}>{user}</span>
          <button onClick={() => setUser(null)} style={{ padding: '5px 10px', backgroundColor: '#ef4444', color: 'white', border: 'none', borderRadius: '3px', cursor: 'pointer' }}>
            Déconnexion
          </button>
        </div>
      </div>
      <div style={{ backgroundColor: '#f3f4f6', padding: '20px', borderRadius: '8px' }}>
        <h2>Bienvenue, {user}</h2>
        <p>Vous êtes connecté à l'application de gestion des heures supplémentaires.</p>
        <div style={{ backgroundColor: '#dbeafe', padding: '15px', borderRadius: '5px', marginTop: '15px' }}>
          <strong>Interface fonctionnelle restaurée</strong>
          <p>Cette version simple fonctionne sans dépendances complexes.</p>
        </div>
      </div>
    </div>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <WorkingApp />
  </React.StrictMode>,
);
