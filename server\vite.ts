import { Express } from "express";
import { Server } from "http";
import path from "path";
import fs from "fs";
import express from "express";

export function log(message: string) {
  console.log(`[server] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  // Create Vite server in middleware mode
  const { createServer: createViteServer } = await import("vite");
  const vite = await createViteServer({
    server: { middlewareMode: true },
    appType: "spa", // Utiliser le mode SPA au lieu de custom
    base: "/",
  });

  // Use vite's connect instance as middleware
  app.use(vite.middlewares);
}

export function serveStatic(app: Express) {
  app.use(express.static(path.join(process.cwd(), "dist")));
  app.get("*", (req, res) => {
    res.sendFile(path.join(process.cwd(), "dist", "index.html"));
  });
}
