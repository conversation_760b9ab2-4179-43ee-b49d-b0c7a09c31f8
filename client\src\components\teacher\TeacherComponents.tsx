import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { useNotification } from '@/hooks/use-notification';
import { CalendarView } from './calendar-view';
import { RecentSessions } from './recent-sessions';
import { DashboardView } from './dashboard-view';
import { SessionForm } from './session-form';
import { SessionDetailModal } from '@/components/session-detail-modal';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import {
  Dialog, DialogContent, DialogHeader,
  DialogTitle, DialogFooter, DialogDescription
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, Calendar as CalendarIcon, List, User, Plus } from 'lucide-react';

// Type pour les sessions
interface Session {
  id: number;
  date: string;
  timeSlot: string;
  type: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  originalType: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  status: string;
  teacherId: number;
  teacherName: string;
  inPacte?: boolean;
  createdAt: string;
  updatedAt?: string;
  updatedBy?: string;

  // Fields specific to RCD
  replacedTeacherPrefix?: string;
  replacedTeacherLastName?: string;
  replacedTeacherFirstName?: string;
  replacedTeacherName?: string;
  className?: string;
  subject?: string;
  comment?: string;

  // Fields specific to Devoirs Faits
  studentCount?: number;
  gradeLevel?: string;

  // Fields specific to Autre
  description?: string;
}

export function TeacherView() {
  const { user } = useAuth();
  const { toast } = useToast();
  const { showNotification } = useNotification();
  const [activeTab, setActiveTab] = useState("dashboard");

  // États pour les modales
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // États pour les données
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | undefined>(undefined);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const [sessionToEdit, setSessionToEdit] = useState<Session | null>(null);
  const [sessionToDelete, setSessionToDelete] = useState<Session | null>(null);

  // Récupérer les sessions pour vérifier les notifications
  const { data: sessions = [] } = useQuery<Session[]>({
    queryKey: ['/api/sessions', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await apiRequest('GET', `/api/sessions/teacher/${user.id}`);
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des sessions');
      }
      return response.json();
    },
    enabled: !!user?.id
  });

  // Vérifier les sessions qui ont changé de statut récemment
  useEffect(() => {
    // Récupérer les sessions déjà vues dans le localStorage
    const viewedSessions = JSON.parse(localStorage.getItem('viewedSessions') || '{}');
    const now = new Date().getTime();

    // Parcourir les sessions pour trouver celles qui ont changé de statut
    sessions.forEach(session => {
      const sessionKey = `session-${session.id}`;
      const lastViewedStatus = viewedSessions[sessionKey]?.status;
      const lastViewedTime = viewedSessions[sessionKey]?.time || 0;

      // Si la session a un statut différent de la dernière fois qu'elle a été vue
      // et que la dernière vue date d'au moins 1 minute (pour éviter les notifications multiples)
      if (lastViewedStatus &&
          lastViewedStatus !== session.status &&
          now - lastViewedTime > 60000) {

        // Afficher une notification
        showNotification({
          title: 'Statut de séance mis à jour',
          description: `La séance du ${format(new Date(session.date), 'dd/MM/yyyy')} (${session.type}) est maintenant "${session.status}"`,
          type: session.status === 'REJECTED' ? 'error' :
                session.status === 'VALIDATED' || session.status === 'PAID' ? 'success' : 'info',
          duration: 8000
        });

        // Mettre à jour le statut vu
        viewedSessions[sessionKey] = {
          status: session.status,
          time: now
        };
      } else if (!viewedSessions[sessionKey]) {
        // Si c'est la première fois qu'on voit cette session, l'enregistrer
        viewedSessions[sessionKey] = {
          status: session.status,
          time: now
        };
      }
    });

    // Sauvegarder les sessions vues
    localStorage.setItem('viewedSessions', JSON.stringify(viewedSessions));
  }, [sessions, showNotification]);

  // Mutation pour supprimer une session
  const deleteSessionMutation = useMutation({
    mutationFn: async (sessionId: number) => {
      const response = await apiRequest('DELETE', `/api/sessions/${sessionId}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Erreur lors de la suppression de la séance');
      }
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/sessions', user?.id] });
      toast({
        title: 'Succès',
        description: 'La séance a été supprimée avec succès',
      });
      setIsDeleteModalOpen(false);
      setSessionToDelete(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Erreur',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Gestionnaires d'événements
  const handleSlotClick = (date: Date, timeSlot: string) => {
    setSelectedDate(date);
    setSelectedTimeSlot(timeSlot);
    setSessionToEdit(null);
    setIsFormOpen(true);
  };

  const handleSessionClick = (session: Session) => {
    setSelectedSession(session);
    setIsDetailModalOpen(true);
  };

  const handleEditSession = (session: Session) => {
    setSessionToEdit(session);
    setIsFormOpen(true);
    setIsDetailModalOpen(false);
  };

  const handleDeleteSession = (session: Session) => {
    setSessionToDelete(session);
    setIsDeleteModalOpen(true);
    setIsDetailModalOpen(false);
  };

  const confirmDeleteSession = () => {
    if (sessionToDelete) {
      deleteSessionMutation.mutate(sessionToDelete.id);
    }
  };

  const handleNewSession = () => {
    setSelectedDate(undefined);
    setSelectedTimeSlot(undefined);
    setSessionToEdit(null);
    setIsFormOpen(true);
  };

  // Le statut pacte est maintenant géré par le secrétariat

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 pt-16 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col space-y-4">
          <header className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Bienvenue, {user?.name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Gestion de vos heures supplémentaires
                </p>
              </div>

              <Button
                onClick={handleNewSession}
                className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">Déclarer des heures</span>
                <span className="sm:hidden">Déclarer</span>
              </Button>
            </div>
          </header>

          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
            <Tabs defaultValue="dashboard" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="border-b border-gray-200 dark:border-gray-700 px-4">
                <TabsList className="bg-transparent h-auto p-0 w-full justify-start">
                  <TabsTrigger
                    value="dashboard"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <span>Tableau de bord</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="calendar"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <CalendarIcon className="h-4 w-4" />
                    <span>Calendrier</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="sessions"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <List className="h-4 w-4" />
                    <span>Mes séances</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="profile"
                    className="flex items-center gap-1 py-3 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                  >
                    <User className="h-4 w-4" />
                    <span>Mon profil</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <div className="p-4">
                <TabsContent value="dashboard" className="mt-0 pt-2">
                  <DashboardView
                    sessions={sessions}
                    onSessionClick={handleSessionClick}
                    onViewAllSessions={() => setActiveTab("sessions")}
                  />
                </TabsContent>

                <TabsContent value="calendar" className="mt-0 pt-2">
                  <CalendarView
                    onSlotClick={handleSlotClick}
                    onSessionClick={handleSessionClick}
                  />
                </TabsContent>

                <TabsContent value="sessions" className="mt-0 pt-2">
                  <RecentSessions
                    onSessionClick={handleSessionClick}
                    onEditSession={handleEditSession}
                    onDeleteSession={handleDeleteSession}
                  />
                </TabsContent>

                <TabsContent value="profile" className="mt-0 pt-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Mon profil</CardTitle>
                      <CardDescription>
                        Gérez vos informations personnelles et vos préférences
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Informations de base */}
                      <div className="space-y-2">
                        <h3 className="text-lg font-medium">Informations personnelles</h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm text-muted-foreground">Nom</Label>
                            <div className="font-medium">{user?.name}</div>
                          </div>
                          <div>
                            <Label className="text-sm text-muted-foreground">Email</Label>
                            <div className="font-medium">{user?.username}</div>
                          </div>
                        </div>
                      </div>

                      {/* Statut Pacte */}
                      <div className="space-y-4 border-t pt-4">
                        <h3 className="text-lg font-medium">Statut Pacte</h3>
                        <div className="flex items-center space-x-2">
                          <div className={`w-4 h-4 rounded-full ${user?.inPacte ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          <Label>
                            {user?.inPacte ? "Je participe au pacte" : "Je ne participe pas au pacte"}
                          </Label>
                        </div>

                        {user?.inPacte && (
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Progression</span>
                              <span className="font-medium">
                                {user.pacteHoursCompleted || 0} / {user.pacteHoursTarget || '?'} heures
                              </span>
                            </div>
                            <div className="relative pt-1">
                              <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                                <div
                                  style={{
                                    width: user.pacteHoursTarget
                                      ? `${Math.min(100, ((user.pacteHoursCompleted || 0) / user.pacteHoursTarget) * 100)}%`
                                      : '0%'
                                  }}
                                  className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"
                                ></div>
                              </div>
                            </div>
                          </div>
                        )}

                        <p className="text-sm text-muted-foreground">
                          Ce statut est défini par le secrétariat selon votre contrat.
                          Il affecte la façon dont vos heures supplémentaires sont comptabilisées.
                        </p>
                      </div>

                      {/* Signature électronique - à implémenter */}
                      <div className="space-y-2 border-t pt-4">
                        <h3 className="text-lg font-medium">Signature électronique</h3>
                        <p className="text-sm text-muted-foreground">
                          Votre signature sera utilisée pour les documents officiels.
                        </p>
                        <div className="border rounded-md p-4 h-32 flex items-center justify-center bg-gray-50">
                          {user?.signature ? (
                            <div>Signature enregistrée</div>
                          ) : (
                            <div className="text-muted-foreground text-center">
                              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                              <p>Aucune signature enregistrée</p>
                            </div>
                          )}
                        </div>
                        <Button variant="outline" className="mt-2">
                          {user?.signature ? "Modifier ma signature" : "Ajouter ma signature"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Modales */}
      <SessionForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        selectedDate={selectedDate}
        selectedTimeSlot={selectedTimeSlot}
        sessionToEdit={sessionToEdit}
      />

      <SessionDetailModal
        session={selectedSession}
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        onEdit={handleEditSession}
        onDelete={handleDeleteSession}
      />

      {/* Modale de confirmation de suppression */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer cette séance ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between">
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
            >
              Annuler
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteSession}
              disabled={deleteSessionMutation.isPending}
            >
              {deleteSessionMutation.isPending ? 'Suppression...' : 'Supprimer'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
