import { useState } from 'react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  Table, TableBody, TableCell, TableHead,
  TableHeader, TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/ui/status-badge";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { SESSION_TYPES, TIME_SLOTS } from "@shared/schema";
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/hooks/use-auth';
import {
  ChevronLeft, ChevronRight,
  FileText, Download, Trash2, Edit,
  Search, Filter
} from 'lucide-react';

// Type pour les sessions
interface Session {
  id: number;
  date: string;
  timeSlot: string;
  type: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  status: string;
  teacherId: number;
  teacherName: string;
  className?: string;
  gradeLevel?: string;
  description?: string;
  createdAt: string;
  updatedAt?: string;
}

interface RecentSessionsProps {
  onSessionClick: (session: Session) => void;
  onEditSession: (session: Session) => void;
  onDeleteSession: (session: Session) => void;
}

export function RecentSessions({
  onSessionClick,
  onEditSession,
  onDeleteSession
}: RecentSessionsProps) {
  const { user } = useAuth();
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [filters, setFilters] = useState({
    type: 'all',
    status: 'all',
    dateRange: 'all',
  });

  // Récupérer les sessions de l'enseignant
  const { data: allSessions = [], isLoading } = useQuery<Session[]>({
    queryKey: ['/api/sessions', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await apiRequest('GET', `/api/sessions/teacher/${user.id}`);
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des sessions');
      }
      return response.json();
    },
    enabled: !!user?.id
  });

  // Filtrer les sessions selon les critères
  const filteredSessions = [...allSessions].filter(session => {
    // Filtre par type
    if (filters.type !== 'all' && session.type !== filters.type) {
      return false;
    }

    // Filtre par statut
    if (filters.status !== 'all' && session.status !== filters.status) {
      return false;
    }

    // Filtre par plage de dates
    if (filters.dateRange !== 'all') {
      const sessionDate = new Date(session.date);
      const today = new Date();

      if (filters.dateRange === 'thisWeek') {
        // Cette semaine
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay() + 1); // Lundi de cette semaine
        startOfWeek.setHours(0, 0, 0, 0);

        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // Dimanche de cette semaine
        endOfWeek.setHours(23, 59, 59, 999);

        if (sessionDate < startOfWeek || sessionDate > endOfWeek) {
          return false;
        }
      } else if (filters.dateRange === 'thisMonth') {
        // Ce mois
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59, 999);

        if (sessionDate < startOfMonth || sessionDate > endOfMonth) {
          return false;
        }
      } else if (filters.dateRange === 'lastMonth') {
        // Mois dernier
        const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59, 999);

        if (sessionDate < startOfLastMonth || sessionDate > endOfLastMonth) {
          return false;
        }
      }
    }

    return true;
  });

  // Trier les sessions par date (les plus récentes d'abord)
  const sortedSessions = [...filteredSessions].sort((a, b) => {
    // D'abord par date (plus récente en premier)
    const dateComparison = new Date(b.date).getTime() - new Date(a.date).getTime();
    if (dateComparison !== 0) return dateComparison;

    // Ensuite par créneau horaire
    const timeSlotOrder = {
      'M1': 1, 'M2': 2, 'M3': 3, 'M4': 4,
      'S1': 5, 'S2': 6, 'S3': 7, 'S4': 8
    };

    return (timeSlotOrder[a.timeSlot as keyof typeof timeSlotOrder] || 0) -
           (timeSlotOrder[b.timeSlot as keyof typeof timeSlotOrder] || 0);
  });

  // Pagination
  const totalPages = Math.ceil(sortedSessions.length / pageSize);
  const paginatedSessions = sortedSessions.slice(
    (page - 1) * pageSize,
    page * pageSize
  );

  // Vérifier si une session est éditable
  const isSessionEditable = (session: Session) => {
    return ['SUBMITTED', 'INCOMPLETE'].includes(session.status);
  };

  // Vérifier si une session est supprimable
  const isSessionDeletable = (session: Session) => {
    return ['SUBMITTED', 'INCOMPLETE'].includes(session.status);
  };

  // Formater la date
  const formatDate = (dateStr: string) => {
    return format(parseISO(dateStr), 'dd/MM/yyyy', { locale: fr });
  };

  // Navigation dans la pagination
  const goToPreviousPage = () => {
    setPage(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setPage(prev => Math.min(totalPages, prev + 1));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Gérer les changements de filtres
  const handleFilterChange = (filterType: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
    setPage(1); // Réinitialiser la pagination lors du changement de filtre
  };

  return (
    <div className="space-y-4">
      {/* Filtres */}
      <div className="bg-white p-4 rounded-md border space-y-4">
        <h3 className="font-medium text-sm">Filtres</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Filtre par type */}
          <div className="space-y-2">
            <Label htmlFor="type-filter" className="text-xs">Type de séance</Label>
            <Select
              value={filters.type}
              onValueChange={(value) => handleFilterChange('type', value)}
            >
              <SelectTrigger id="type-filter">
                <SelectValue placeholder="Tous les types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les types</SelectItem>
                <SelectItem value="RCD">RCD</SelectItem>
                <SelectItem value="DEVOIRS_FAITS">Devoirs Faits</SelectItem>
                <SelectItem value="HSE">HSE</SelectItem>
                <SelectItem value="AUTRE">Autre</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filtre par statut */}
          <div className="space-y-2">
            <Label htmlFor="status-filter" className="text-xs">Statut</Label>
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger id="status-filter">
                <SelectValue placeholder="Tous les statuts" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="SUBMITTED">Soumise</SelectItem>
                <SelectItem value="INCOMPLETE">Incomplète</SelectItem>
                <SelectItem value="REVIEWED">Vérifiée</SelectItem>
                <SelectItem value="VALIDATED">Validée</SelectItem>
                <SelectItem value="READY_FOR_PAYMENT">Prête pour paiement</SelectItem>
                <SelectItem value="PAID">Payée</SelectItem>
                <SelectItem value="REJECTED">Refusée</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filtre par date */}
          <div className="space-y-2">
            <Label htmlFor="date-filter" className="text-xs">Période</Label>
            <Select
              value={filters.dateRange}
              onValueChange={(value) => handleFilterChange('dateRange', value)}
            >
              <SelectTrigger id="date-filter">
                <SelectValue placeholder="Toutes les dates" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les dates</SelectItem>
                <SelectItem value="thisWeek">Cette semaine</SelectItem>
                <SelectItem value="thisMonth">Ce mois-ci</SelectItem>
                <SelectItem value="lastMonth">Mois dernier</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Créneau</TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="hidden md:table-cell">Détails</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedSessions.length > 0 ? (
              paginatedSessions.map((session) => (
                <TableRow
                  key={session.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSessionClick(session)}
                >
                  <TableCell>{formatDate(session.date)}</TableCell>
                  <TableCell>
                    {TIME_SLOTS[session.timeSlot as keyof typeof TIME_SLOTS]?.label}
                  </TableCell>
                  <TableCell>
                    <Badge variant={SESSION_TYPES[session.type as keyof typeof SESSION_TYPES]?.color as any}>
                      {SESSION_TYPES[session.type as keyof typeof SESSION_TYPES]?.label}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden md:table-cell max-w-[200px] truncate">
                    {session.type === 'RCD' && session.className && (
                      <span>Classe {session.className}</span>
                    )}
                    {session.type === 'DEVOIRS_FAITS' && session.gradeLevel && (
                      <span>Niveau {session.gradeLevel}</span>
                    )}
                    {session.type === 'AUTRE' && session.description && (
                      <span className="truncate">{session.description}</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={session.status as any} />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2" onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onSessionClick(session)}
                        title="Voir les détails"
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                      {isSessionEditable(session) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onEditSession(session)}
                          title="Modifier"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      {isSessionDeletable(session) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDeleteSession(session)}
                          title="Supprimer"
                          className="text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                  Aucune session trouvée
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Page {page} sur {totalPages}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousPage}
              disabled={page === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Précédent
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={page === totalPages}
            >
              Suivant
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
