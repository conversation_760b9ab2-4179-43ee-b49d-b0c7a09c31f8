import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, LogIn, Info } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Définition des profils utilisateurs pour les tests
const TEST_PROFILES = [
  { name: '<PERSON>', role: 'Enseignante sans pacte', username: '<EMAIL>', password: 'password123' },
  { name: '<PERSON>', role: 'Enseignante avec pacte', username: '<EMAIL>', password: 'password123' },
  { name: '<PERSON>', role: 'Enseignant sans pacte', username: '<EMAIL>', password: 'password123' },
  { name: '<PERSON>', role: 'Enseignant avec pacte', username: '<EMAIL>', password: 'password123' },
  { name: 'Laure Martin', role: 'Secrétariat', username: '<EMAIL>', password: 'password123' },
  { name: 'Jean Dupont', role: 'Direction', username: '<EMAIL>', password: 'password123' },
  { name: 'Admin', role: 'Administrateur', username: '<EMAIL>', password: 'password123' },
];

export default function AuthPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [selectedProfile, setSelectedProfile] = useState<number | null>(null);
  const { loginMutation, user, error } = useAuth();
  const [, setLocation] = useLocation();
  const [autoLoginDisabled, setAutoLoginDisabled] = useState(false);

  // Rediriger si déjà connecté
  useEffect(() => {
    if (user) {
      setLocation('/');
    }
  }, [user, setLocation]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loginMutation.mutate({ username, password });
  };

  const handleProfileSelect = (index: number) => {
    setSelectedProfile(index);
    setUsername(TEST_PROFILES[index].username);
    setPassword(TEST_PROFILES[index].password);
  };

  const handleQuickLogin = () => {
    if (selectedProfile !== null) {
      loginMutation.mutate({
        username: TEST_PROFILES[selectedProfile].username,
        password: TEST_PROFILES[selectedProfile].password
      });
    }
  };

  const toggleAutoLogin = () => {
    setAutoLoginDisabled(!autoLoginDisabled);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-100 to-white">
      <Card className="w-full max-w-md shadow-xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-500 px-6 py-8 text-white">
          <CardTitle className="text-3xl font-bold text-center mb-2">Gestion des Heures Supplémentaires</CardTitle>
          <CardDescription className="text-center text-blue-100">
            Veuillez sélectionner un profil pour vous connecter
          </CardDescription>
        </CardHeader>

        {autoLoginDisabled && (
          <Alert className="mx-4 mt-4 mb-2" variant="info">
            <Info className="h-4 w-4" />
            <AlertDescription>
              Vous vous êtes déconnecté. La connexion automatique est désactivée.
              Choisissez un profil pour vous connecter à nouveau.
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="quick" className="w-full">
          <TabsList className="grid w-full grid-cols-2 p-1 mx-4 mt-4 bg-blue-100 rounded-md">
            <TabsTrigger value="quick" className="rounded-md data-[state=active]:bg-white data-[state=active]:text-blue-600">Connexion rapide</TabsTrigger>
            <TabsTrigger value="manual" className="rounded-md data-[state=active]:bg-white data-[state=active]:text-blue-600">Connexion manuelle</TabsTrigger>
          </TabsList>

          <TabsContent value="quick" className="space-y-4">
            <CardContent className="pt-4">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-blue-800 mb-4 text-center">
                  Choisissez un profil pour tester
                </h3>

                <div className="space-y-2 mb-6">
                  {TEST_PROFILES.map((profile, index) => (
                    <button
                      key={index}
                      className={`w-full text-left px-4 py-3 rounded-md transition-all ${
                        selectedProfile === index
                          ? 'bg-blue-600 text-white'
                          : 'bg-white border border-blue-200 hover:border-blue-400 hover:bg-blue-50'
                      }`}
                      onClick={() => handleProfileSelect(index)}
                    >
                      <div className="font-medium">{profile.name}</div>
                      <div className={`text-sm ${selectedProfile === index ? 'text-blue-100' : 'text-blue-600'}`}>
                        {profile.role}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full bg-blue-600 hover:bg-blue-700"
                onClick={handleQuickLogin}
                disabled={selectedProfile === null || loginMutation.isPending}
              >
                {loginMutation.isPending ? 'Connexion en cours...' : 'Se connecter maintenant'}
              </Button>
            </CardFooter>
          </TabsContent>

          <TabsContent value="manual">
            <CardContent className="pt-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Erreur</AlertTitle>
                    <AlertDescription>
                      {error.message || 'Une erreur est survenue lors de la connexion.'}
                    </AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-blue-800">Adresse e-mail</Label>
                  <Input
                    id="username"
                    type="email"
                    placeholder="<EMAIL>"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="border-blue-200 focus:border-blue-400 focus:ring-blue-400"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-blue-800">Mot de passe</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="border-blue-200 focus:border-blue-400 focus:ring-blue-400"
                    required
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  disabled={loginMutation.isPending}
                >
                  {loginMutation.isPending ? 'Connexion en cours...' : 'Se connecter'}
                </Button>
              </form>
            </CardContent>
          </TabsContent>
        </Tabs>

        <CardFooter className="flex flex-col space-y-2 pt-0 pb-4">
          <Button
            variant="link"
            className="text-sm text-blue-600 hover:text-blue-800"
            onClick={toggleAutoLogin}
          >
            {autoLoginDisabled ? 'Réactiver la connexion automatique' : 'Désactiver la connexion automatique'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
