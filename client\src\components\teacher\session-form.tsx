import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  Dialog, DialogContent, DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';

// Composants de formulaire spécifiques
import { SessionTypeSelector } from './session-type-selector';
import { RCDForm } from './rcd-form';
import { DevoirsFaitsForm } from './devoirs-faits-form';
import { AutreForm } from './autre-form';
import { SessionConfirmation } from './session-confirmation';

// Type pour les sessions
interface Session {
  id?: number;
  date: string;
  timeSlot: string;
  type: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE';
  teacherId: number;
  teacherName: string;
  status?: string;
  className?: string;
  replacedTeacherPrefix?: string;
  replacedTeacherLastName?: string;
  replacedTeacherFirstName?: string;
  gradeLevel?: string;
  studentCount?: number;
  description?: string;
  comment?: string;
}

interface SessionFormProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate?: Date;
  selectedTimeSlot?: string;
  sessionToEdit?: Session | null;
}

export function SessionForm({
  isOpen,
  onClose,
  selectedDate,
  selectedTimeSlot,
  sessionToEdit
}: SessionFormProps) {
  const { toast } = useToast();
  const { user } = useAuth();

  // États pour gérer les étapes du formulaire
  const [step, setStep] = useState<'type' | 'form' | 'confirmation'>('type');
  const [sessionType, setSessionType] = useState<'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE'>(
    sessionToEdit?.type || 'RCD'
  );
  const [formData, setFormData] = useState<Partial<Session>>({
    date: selectedDate ? format(selectedDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
    timeSlot: selectedTimeSlot || 'M1',
    type: sessionToEdit?.type || 'RCD',
    teacherId: user?.id || 0,
    teacherName: user?.name || '',
  });

  // Initialiser le formulaire avec les données de la session à éditer
  useEffect(() => {
    if (sessionToEdit) {
      setSessionType(sessionToEdit.type);
      setFormData({
        ...sessionToEdit
      });
      setStep('form'); // Aller directement au formulaire si on édite
    } else {
      // Réinitialiser le formulaire
      setSessionType(sessionToEdit?.type || 'RCD');
      setFormData({
        date: selectedDate ? format(selectedDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
        timeSlot: selectedTimeSlot || 'M1',
        type: sessionToEdit?.type || 'RCD',
        teacherId: user?.id || 0,
        teacherName: user?.name || '',
      });

      // Si une date et un créneau sont déjà sélectionnés, aller directement à la sélection du type
      if (selectedDate && selectedTimeSlot) {
        setStep('type');
      } else {
        setStep('type');
      }
    }
  }, [sessionToEdit, selectedDate, selectedTimeSlot, user, isOpen]);

  // Mutation pour créer une nouvelle session
  const createSessionMutation = useMutation({
    mutationFn: async (sessionData: Partial<Session>) => {
      const response = await apiRequest('POST', '/api/sessions', sessionData);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Erreur lors de la création de la séance');
      }
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/sessions', user?.id] });
      toast({
        title: 'Succès',
        description: 'La séance a été créée avec succès',
      });
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: 'Erreur',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Mutation pour mettre à jour une session existante
  const updateSessionMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<Session> }) => {
      const response = await apiRequest('PATCH', `/api/sessions/${id}`, data);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Erreur lors de la mise à jour de la séance');
      }
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/sessions', user?.id] });
      toast({
        title: 'Succès',
        description: 'La séance a été mise à jour avec succès',
      });
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: 'Erreur',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Gérer la sélection du type de session
  const handleTypeSelect = (type: 'RCD' | 'DEVOIRS_FAITS' | 'HSE' | 'AUTRE') => {
    setSessionType(type);
    setFormData(prev => ({
      ...prev,
      type,
      // Réinitialiser les champs spécifiques au type
      className: undefined,
      replacedTeacherPrefix: undefined,
      replacedTeacherLastName: undefined,
      replacedTeacherFirstName: undefined,
      gradeLevel: undefined,
      studentCount: undefined,
      description: undefined,
    }));
    setStep('form');
  };

  // Gérer la soumission du formulaire RCD
  const handleRCDSubmit = (data: any) => {
    const updatedData = {
      ...formData,
      ...data,
      type: 'RCD',
    };
    setFormData(updatedData);
    setStep('confirmation');
  };

  // Gérer la soumission du formulaire Devoirs Faits
  const handleDevoirsFaitsSubmit = (data: any) => {
    const updatedData = {
      ...formData,
      ...data,
      type: 'DEVOIRS_FAITS',
      studentCount: parseInt(data.studentCount),
    };
    setFormData(updatedData);
    setStep('confirmation');
  };

  // Gérer la soumission du formulaire HSE/Autre
  const handleAutreSubmit = (data: any) => {
    const updatedData = {
      ...formData,
      ...data,
      type: sessionType, // HSE ou AUTRE
    };
    setFormData(updatedData);
    setStep('confirmation');
  };

  // Gérer la confirmation finale
  const handleConfirmSubmit = () => {
    if (sessionToEdit?.id) {
      // Mise à jour d'une session existante
      updateSessionMutation.mutate({
        id: sessionToEdit.id,
        data: formData
      });
    } else {
      // Création d'une nouvelle session
      createSessionMutation.mutate(formData);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {sessionToEdit ? 'Modifier la séance' : 'Nouvelle séance'}
          </DialogTitle>
        </DialogHeader>

        {/* Étape 1: Sélection du type de séance */}
        {step === 'type' && (
          <SessionTypeSelector onSelect={handleTypeSelect} />
        )}

        {/* Étape 2: Formulaire spécifique au type */}
        {step === 'form' && (
          <>
            {sessionType === 'RCD' && (
              <RCDForm
                initialData={sessionToEdit}
                onSubmit={handleRCDSubmit}
                onCancel={onClose}
                selectedDate={selectedDate}
                selectedTimeSlot={selectedTimeSlot}
              />
            )}

            {sessionType === 'DEVOIRS_FAITS' && (
              <DevoirsFaitsForm
                initialData={sessionToEdit}
                onSubmit={handleDevoirsFaitsSubmit}
                onCancel={onClose}
                selectedDate={selectedDate}
                selectedTimeSlot={selectedTimeSlot}
              />
            )}

            {(sessionType === 'HSE' || sessionType === 'AUTRE') && (
              <AutreForm
                initialData={sessionToEdit}
                onSubmit={handleAutreSubmit}
                onCancel={onClose}
                selectedDate={selectedDate}
                selectedTimeSlot={selectedTimeSlot}
              />
            )}
          </>
        )}

        {/* Étape 3: Confirmation */}
        {step === 'confirmation' && (
          <SessionConfirmation
            sessionData={formData}
            onConfirm={handleConfirmSubmit}
            onCancel={() => setStep('form')}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
