import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";

export default function UserRoleSwitcher() {
  const { user } = useAuth();

  // Si l'utilisateur n'est pas connecté ou n'est pas un administrateur, ne pas afficher le sélecteur
  if (!user || user.role !== "ADMIN") {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          Changer de rôle <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem className="cursor-pointer">
          Administrateur
        </DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">
          Direction
        </DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">
          Secrétariat
        </DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">
          Enseignant
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
